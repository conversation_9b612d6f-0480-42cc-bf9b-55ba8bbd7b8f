<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    Route::get('purchases', function () {
        return Inertia::render('purchases/index');
    })->name('purchases');
    Route::get('purchases/input', function () {
        return Inertia::render('purchases/input');
    })->name('purchases.input');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
