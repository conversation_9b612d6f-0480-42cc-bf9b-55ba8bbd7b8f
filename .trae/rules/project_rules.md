# Project Rules – <PERSON><PERSON> 12 + Inertia v2 (React)

> **<PERSON>ope (Dev‑Only)**: Local development for **<PERSON><PERSON> 12** + **Inertia v2 (React)** via **<PERSON>vel Sail** (Docker) with **MariaDB** and **Redis**. Base URL: **[http://localhost](http://localhost)**. **Owner-only handles staging/production, CI/CD, and dependency decisions.**

> **Rules of Engagement**
>
> 1. _Dev focus only_ — anything that touches deploy, infra, or CI/CD is out of scope.
> 2. _Package-agnostic_ — suggestions here are guidance only. **Never add/replace packages without Owner confirmation.**
> 3. _Stability first_ — no broken local build, no browser console errors, and no failing tests allowed on `main`.

---

## 1) Stack & Versions (Development Focus)

- **PHP**: 8.4 (Sail image)
- **<PERSON><PERSON>**: 12.x
- **Node**: 20.x LTS
- **React**: 18.x or 19.x (stick to what is stable in the app)
- **Inertia**: v2.x, `@inertiajs/react`
- **DB**: MariaDB 10.6+ (utf8mb4)
- **Cache/Queue/Session**: Redis 7+
- **Testing**: Pest 3 (PHP), Vitest/JSDOM (React) — _optional until confirmed_
- **QA Tooling**: PHPStan (Larastan), Laravel Pint, ESLint, Prettier, TypeScript — _all configurable per Owner approval_

> **Version Control Rule**: Keep `composer.lock` and `pnpm-lock.yaml` committed. **Do not upgrade** without explicit Owner approval.

---

## 2) Local Environment (Sail)

- Services: `laravel.test` (php-fpm + nginx), `mariadb`, `redis`, optional `mailpit`.
- Default ports: **[http://localhost](http://localhost)** (app), `3306` (DB), `6379` (Redis).
- `.env.example` must be accurate. Required keys:
    - `APP_URL=http://localhost`, `APP_ENV=local`, `APP_DEBUG=true`
    - `DB_CONNECTION=mariadb`
    - `CACHE_DRIVER=redis`, `QUEUE_CONNECTION=redis`, `SESSION_DRIVER=redis`

- **One‑shot boot**: `make up` (or `./vendor/bin/sail up -d`) should start the app.

### Local Dev Command Cheat‑Sheet (current scripts)

- Composer dev stack: `composer dev` → serves app, queue listener, logs (Pail), and Vite concurrently.
- SSR dev (optional): `composer dev:ssr` (requires SSR setup).
- Tests: `composer test`.
- Frontend only: `pnpm dev`, build: `pnpm build` / `pnpm build:ssr`.

---

## 3) Git Workflow

- Single developer (owner only). Keep **main** always stable.
- Use feature branches (`feat/*`, `fix/*`, `chore/*`) even if working solo for traceability.
- Use **Conventional Commits** (`feat(auth): add 2FA`).

---

## 4) Code Style & QA

- **PHP**: Laravel Pint (PSR-12), PHPStan (Larastan) — levels per Owner.
- **JS/TS**: ESLint + Prettier (configs already in repo).
- Husky pre-commit hooks are optional for solo workflow.
- **Bug Bar (Dev)**: before pushing to `main`, ensure:
    1. `composer test` passes (if tests exist),
    2. `pnpm types` and `pnpm lint` pass,
    3. Browser console **clean** on key pages (no errors/warnings).

- **No suppression** of lints/static-analysis without an inline comment + TODO with rationale.

---

## 5) Project Structure

- Laravel default for backend (Controllers slim, Services/Actions for logic).
- React: feature-first under `resources/js/features/<feature>`.
- Naming:
    - StudlyCase (PHP classes)
    - PascalCase (React components)
    - camelCase (functions)
    - kebab-case (routes, folders)

---

## 6) Inertia + React Usage

- Pages in `resources/js/pages`; app shell via shared `AppLayout`.
- **TypeScript** for new code. Provide `PageProps` types per page.
- **Validation**: Prefer `FormRequest` on server; optionally mirror with Zod on client (confirm with Owner before adding packages).
- **Data fetching**: initial data via Inertia props; client re-fetch via a simple `fetch` wrapper. **TanStack Query is optional** — add only if confirmed.
- Forms: Inertia forms or `react-hook-form` (if approved). Always show server validation errors from Form Requests.

---

## 7) Database & Migrations

- Charset: `utf8mb4`, collation: `utf8mb4_unicode_ci`.
- Naming: plural snake_case (`order_items`), FKs with `foreignId()->constrained()`.
- Index all FKs & search columns.
- Use **transactions** in Services.
- Seeds: factories for tests only; avoid polluting real dev DB.

---

## 8) Caching, Queues, Jobs

- Redis for cache/session/queue (local only).
- Prefix cache keys per feature.
- Jobs: add retries/backoff as needed; queues run locally via `composer dev` process.

---

## 9) File Handling

- Store uploads under `storage/app` → symlink `public/storage`.
- Validate MIME/size before saving.
- Use `maatwebsite/excel` for spreadsheets, `dompdf`/Browsershot for PDFs if needed.

---

## 10) Security (Dev Focus)

- Secrets in `.env` only; never commit real values.
- CSRF handled by middleware.
- Auth baseline: Laravel Breeze.
- Log sensitive events locally.

---

## 11) Performance Practices

- Enable `Model::preventLazyLoading()` in local.
- Use pagination; no Inertia prop with >100 records.
- Lazy load React routes with Vite.

---

## 12) Error Handling

- Exceptions handled in `Handler.php`.
- Show friendly errors locally.
- No stack traces exposed to UI.

---

## 13) Testing

- Backend: Pest (feature tests for routes/policies; unit tests for Services/Actions) — start small and expand.
- Frontend: Vitest for critical components/hooks (optional until approved).
- **Rule**: No broken tests in `main`. When adding tests, ensure they run quickly in local Sail.

---

## 14) Docs

- Maintain `/docs` with README, ENV.md, DB_SCHEMA.md.
- Keep ADRs for big decisions.

---

## 15) Deployment & Production (Out of Scope for These Rules)

- **Owner-only** handles deploy, CI/CD, secrets, and prod incidents.
- These rules cover **local development only**.
- Production bugfixes: always test locally first.

---

## 16) Troubleshooting (Localhost)

- If app not loading: check Sail containers, `.env`, run `artisan key:generate`, `artisan migrate --seed`, clear caches.
- Permissions: ensure `storage/` + `bootstrap/cache` writable by container.

---

**Owner**: Wahyu
**Last updated**: 15/9/2025
**Rule**: Project rules are dev-focused. For production, only Owner decides and executes. Confirm before adding new tools/packages.
