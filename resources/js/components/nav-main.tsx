import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { SidebarGroup, SidebarGroupLabel, SidebarMenu } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronDown, ChevronRight, Dot } from 'lucide-react';
import { useState } from 'react';

export function NavMain({ items = [] }: { items: NavItem[] }) {
    const page = usePage();

    const pathname = page.url;
    const [openItems, setOpenItems] = useState<string[]>([]);

    const toggleItem = (name: string) => {
        setOpenItems((prev) => (prev.includes(name) ? prev.filter((item) => item !== name) : [...prev, name]));
    };
    return (
        <SidebarGroup className="px-2 py-0">
            <SidebarGroupLabel>Platform</SidebarGroupLabel>
            <SidebarMenu>
                {items.map((item) => {
                    if (item.children) {
                        const isOpen = openItems.includes(item.title);
                        const hasActiveChild = item.children.some((child) => pathname === child.href);

                        return (
                            <Collapsible key={item.title} open={isOpen} onOpenChange={() => toggleItem(item.title)}>
                                <CollapsibleTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        className={cn(
                                            'w-full justify-start',
                                            (isOpen || hasActiveChild) && 'bg-sidebar-accent text-sidebar-accent-foreground',
                                        )}
                                    >
                                        {item.icon && <item.icon className="mr-2 h-4 w-4" />}
                                        {item.title}
                                        {isOpen ? <ChevronDown className="ml-auto h-4 w-4" /> : <ChevronRight className="ml-auto h-4 w-4" />}
                                    </Button>
                                </CollapsibleTrigger>
                                <CollapsibleContent className="mt-1 space-y-1">
                                    {item.children.map((child) => (
                                        <Button
                                            key={child.href?.toString() ?? child.title}
                                            variant="ghost"
                                            size="sm"
                                            asChild
                                            className={cn(
                                                'w-full justify-start pl-8',
                                                pathname === child.href && 'bg-sidebar-primary text-sidebar-primary-foreground',
                                            )}
                                        >
                                            <Link href={child.href}>
                                                <Dot className="mr-2 h-4 w-4" /> {child.title}
                                            </Link>
                                        </Button>
                                    ))}
                                </CollapsibleContent>
                            </Collapsible>
                        );
                    }

                    const isActive =
                        item.href &&
                        (typeof item.href === 'string'
                            ? pathname === item.href || pathname.startsWith(item.href + '/')
                            : pathname === item.href.url || pathname.startsWith(item.href.url + '/'));

                    return (
                        <Button
                            key={item.title}
                            variant="ghost"
                            asChild
                            className={cn('w-full justify-start', isActive && 'bg-sidebar-primary/20 text-sidebar-primary-foreground')}
                        >
                            <Link href={item.href}>
                                {item.icon && <item.icon className="mr-2 h-2 w-2" />}
                                {item.title}
                            </Link>
                        </Button>
                    );
                })}
            </SidebarMenu>
        </SidebarGroup>
    );
}
