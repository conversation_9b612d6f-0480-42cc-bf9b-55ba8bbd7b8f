import { dashboard } from '@/routes';
import { type NavItem } from '@/types';
import { Calendar, FileText, LayoutGrid, Settings, ShoppingCart, TrendingUp } from 'lucide-react';

export const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: dashboard(),
        icon: LayoutGrid,
    },
    {
        title: 'Pembelian',
        icon: ShoppingCart,
        children: [
            { title: 'Pembelian', href: '/purchases' },
            { title: 'Input BOH', href: '/purchases/input' },
            { title: 'Transform & Preview', href: '/purchases/transform' },
            { title: 'Mapping & Validasi', href: '/purchases/mapping' },
            { title: 'Penerima<PERSON>', href: '/purchases/receiving' },
            { title: 'Histori & Audit', href: '/purchases/history' },
        ],
    },
    {
        title: 'Penjualan & Rekonsiliasi',
        icon: TrendingUp,
        children: [
            { title: 'Penjualan', href: '/sales' },
            { title: 'Upload Penjualan BI', href: '/sales/upload-bi' },
            { title: 'Upload Rekening Koran', href: '/sales/upload-bank' },
            { title: 'Match & Verifikasi', href: '/sales/verification' },
            { title: 'Review Selisih', href: '/sales/review' },
            { title: 'Resume Rekonsiliasi', href: '/sales/resume' },
        ],
    },
    // {
    //     title: 'Master & Mapping',
    //     icon: Database,
    //     children: [
    //         { title: 'COA Mapping', href: '/master/coa' },
    //         { title: 'Item/Product Mapping', href: '/master/items' },
    //         { title: 'Channel Mapping', href: '/master/channels' },
    //         { title: 'Bank Mapping', href: '/master/banks' },
    //         { title: 'Tax & Fee Rules', href: '/master/tax-rules' },
    //     ],
    // },
    {
        title: 'Integrasi & Scheduler',
        icon: Calendar,
        children: [
            { title: 'Koneksi BOH', href: '/integration/boh' },
            { title: 'Koneksi BI Tomoro', href: '/integration/bi' },
            { title: 'Koneksi Accurate', href: '/integration/accurate' },
            { title: 'Koneksi Bank', href: '/integration/bank' },
            { title: 'Scheduler Jobs', href: '/integration/scheduler' },
        ],
    },
    {
        title: 'Audit Log & Notifikasi',
        icon: FileText,
        children: [
            { title: 'Activity Logs', href: '/audit/logs' },
            { title: 'Error Center', href: '/audit/errors' },
            { title: 'Notifikasi', href: '/audit/notifications' },
        ],
    },
    {
        title: 'Pengaturan',
        href: '/settings',
        icon: Settings,
    },
];

// export const footerNavItems: NavItem[] = [
//     {
//         title: 'Repository',
//         href: 'https://github.com/laravel/react-starter-kit',
//         icon: Folder,
//     },
//     {
//         title: 'Documentation',
//         href: 'https://laravel.com/docs/starter-kits#react',
//         icon: BookOpen,
//     },
// ];
