
# -------------------------------------------
# Makefile — <PERSON><PERSON> 12 + Sai<PERSON> (MariaDB + Redis)
# Usage: make <target>
# Notes:
#  - Works on macOS, Linux, and WSL2 (Windows) environments.
#  - Customize service names if you changed them in docker-compose.yml.
# -------------------------------------------

SHELL := /bin/bash
# Use bash pipefail to catch errors in pipelines
.SHELLFLAGS := -o pipefail -c

# --- Paths & Commands ---
SAIL := ./vendor/bin/sail
APP_SERVICE ?= laravel.test
DB_SERVICE ?= mariadb           # change to "mysql" if you use MySQL service name
REDIS_SERVICE ?= redis
COMPOSER ?= composer
NODE ?= npm
TTY := $(shell test -t 1 && echo -t)

# --- Helpers ---
.DEFAULT_GOAL := help
.PHONY: help init up down restart build ps logs shell tinker art composer npm fresh migrate seed rollback reset \
        cache-clear optimize test pest queue-work queue-restart horizon redis-cli db-cli db-wait db-dump db-restore \
        perms fix-ownership clean prune

## Show this help (default)
help:
	@awk 'BEGIN {FS = ":.*##"; printf "\n\033[1mMake targets\033[0m\n\n"} \
		/^[a-zA-Z0-9_.-]+:.*?##/ { printf "  \033[36m%-18s\033[0m %s\n", $$1, $$2 } \
		/^##/ { sub(/^## /, "", $$0); print "\n" $$0 "\n" }' $(MAKEFILE_LIST)

# --- Bootstrap / Lifecycle ---

## One-time project init: copy .env, generate key, build & start, install deps
init:
	@if [ ! -f .env ]; then cp .env.example .env; fi
	@$(SAIL) build
	@$(SAIL) up -d
	@$(SAIL) artisan key:generate
	@$(SAIL) composer install --no-interaction
	@$(SAIL) $(NODE) install
	@$(SAIL) $(NODE) run build

## Start containers in background
up:
	@$(SAIL) up -d

## Stop and remove containers
down:
	@$(SAIL) down

## Restart app containers
restart:
	@$(SAIL) restart

## Build/rebuild images (no cache)
build:
	@$(SAIL) build --no-cache

## Container status
ps:
	@$(SAIL) ps

## Tail app logs
logs:
	@$(SAIL) logs -f $(APP_SERVICE)

## Shell into the app container
shell:
	@$(SAIL) exec $(TTY) $(APP_SERVICE) bash

# --- Laravel Utilities ---

## Open Tinker
tinker:
	@$(SAIL) artisan tinker

## Run any artisan command (usage: make art ARGS="cache:clear")
art:
	@test -n "$(ARGS)" || (echo "Usage: make art ARGS=\"<artisan command>\"" && exit 2)
	@$(SAIL) artisan $(ARGS)

## Composer passthrough (usage: make composer ARGS="require laravel/pint --dev")
composer:
	@test -n "$(ARGS)" || (echo "Usage: make composer ARGS=\"<composer args>\"" && exit 2)
	@$(SAIL) composer $(ARGS)

## NPM passthrough (usage: make npm ARGS=\"run dev\")
npm:
	@test -n "$(ARGS)" || (echo "Usage: make npm ARGS=\"<npm args>\"" && exit 2)
	@$(SAIL) $(NODE) $(ARGS)

## Fresh migrate + seed
fresh:
	@$(SAIL) artisan migrate:fresh --seed

## Run migrations
migrate:
	@$(SAIL) artisan migrate

## Seed database (usage: make seed ARGS=\"--class=UserSeeder\")
seed:
	@$(SAIL) artisan db:seed $(ARGS)

## Rollback last batch
rollback:
	@$(SAIL) artisan migrate:rollback

## Reset all migrations
reset:
	@$(SAIL) artisan migrate:reset

## Clear & rebuild caches
cache-clear:
	@$(SAIL) artisan cache:clear
	@$(SAIL) artisan config:clear
	@$(SAIL) artisan route:clear
	@$(SAIL) artisan view:clear

## Optimize caches
optimize:
	@$(SAIL) artisan optimize

## Run PHPUnit tests
test:
	@$(SAIL) artisan test --colors=always

## Run Pest tests (if installed)
pest:
	@$(SAIL) ./vendor/bin/pest -p

# --- Queues / Horizon ---

## Queue worker (stop with Ctrl+C)
queue-work:
	@$(SAIL) artisan queue:work --tries=3

## Restart queue workers
queue-restart:
	@$(SAIL) artisan queue:restart

## Horizon (if installed) — run once
horizon:
	@$(SAIL) artisan horizon

# --- Datastores ---

## Redis CLI inside the redis container
redis-cli:
	@$(SAIL) exec $(REDIS_SERVICE) redis-cli

## MariaDB/MySQL CLI inside the DB container
db-cli:
	@$(SAIL) exec $(DB_SERVICE) mysql -u$$MYSQL_USER -p$$MYSQL_PASSWORD $$MYSQL_DATABASE

## Wait for DB to be ready (useful in CI)
db-wait:
	@$(SAIL) exec $(DB_SERVICE) sh -lc 'until mysqladmin ping -h $$MYSQL_HOST -u$$MYSQL_USER -p$$MYSQL_PASSWORD --silent; do echo "Waiting for DB..."; sleep 2; done'

## Dump database to dumps/YYYYmmdd-HHMMSS.sql
db-dump:
	@mkdir -p dumps
	@TS=$$(date +"%Y%m%d-%H%M%S"); \
	$(SAIL) exec $(DB_SERVICE) sh -lc 'mysqldump -u$$MYSQL_USER -p$$MYSQL_PASSWORD $$MYSQL_DATABASE' > dumps/$$TS.sql; \
	echo "Dump saved: dumps/$$TS.sql"

## Restore database from file (usage: make db-restore FILE=path/to/dump.sql)
db-restore:
	@test -n "$(FILE)" || (echo "Usage: make db-restore FILE=path/to/dump.sql" && exit 2)
	@cat $(FILE) | $(SAIL) exec -T $(DB_SERVICE) sh -lc 'mysql -u$$MYSQL_USER -p$$MYSQL_PASSWORD $$MYSQL_DATABASE'

# --- Filesystem & Cleanup ---

## Fix storage/bootstrap permissions (Unix/WSL)
perms:
	@$(SAIL) exec $(APP_SERVICE) sh -lc 'chown -R www-data:www-data storage bootstrap/cache && chmod -R ug+rwX storage bootstrap/cache'

## Quick fix ownership for current user (for dev bind mounts)
fix-ownership:
	@$(SAIL) exec $(APP_SERVICE) sh -lc 'chown -R $$USER:$$USER . || true'

## Clean caches, vendor, and node_modules (local only)
clean:
	@rm -rf bootstrap/cache/*.php vendor node_modules

## Prune stopped containers/images (Docker)
prune:
	@docker system prune -af --volumes
